[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "morphik"
version = "0.2.10"
authors = [
    { name = "<PERSON>rphi<PERSON>", email = "<EMAIL>" },
]
description = "Morphik Python Client"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "httpx>=0.24.0",
    "pyjwt>=2.0.0",
    "pydantic>=2.10.3",
    "requests>=2.32.3",
    "pillow>=10.4.0"
]

[tool.hatch.build.targets.wheel]
packages = ["morphik"]

[tool.hatch.build.targets.sdist]
include = [
    "/morphik",
]

[build-system]
requires = [
    "setuptools>=45",
    "wheel",
    "pybind11>=2.6.0",
    "numpy>=1.19.0",
    "protobuf>=3.19.0",
]
build-backend = "setuptools.build_meta"

[project]
name = "fixed-dimensional-encoding"
version = "0.1.0"
description = "Python bindings for Fixed Dimensional Encoding of point clouds"
readme = "README.md"
requires-python = ">=3.8"
license = "Apache-2.0"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "numpy>=1.19.0",
    "protobuf>=3.19.0",
]

[tool.cibuildwheel]
# Skip PyPy and musllinux builds
skip = "pp* *musllinux*"
build-verbosity = 3

# Test that the wheel works
test-requires = "numpy"
test-command = "python {project}/test_wheel.py"

# Environment variables for all platforms
[tool.cibuildwheel.environment]
CXXFLAGS = "-std=c++17"

# Linux-specific configuration
[tool.cibuildwheel.linux]
manylinux-x86_64-image = "manylinux_2_28"
archs = ["x86_64"]
environment = { PATH = "/usr/bin:/usr/local/bin:$PATH" }
before-all = """
    # Install dependencies for AlmaLinux 8 (manylinux_2_28)
    yum install -y epel-release
    yum install -y eigen3-devel protobuf-devel protobuf-compiler cmake git gcc-c++

    # Ensure protoc is in PATH
    export PATH="/usr/bin:/usr/local/bin:$PATH"

    # Build and install Abseil
    cd /tmp
    git clone --depth 1 --branch 20240116.1 https://github.com/abseil/abseil-cpp.git
    cd abseil-cpp
    mkdir build && cd build
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_POSITION_INDEPENDENT_CODE=ON -DCMAKE_CXX_STANDARD=17 -DABSL_PROPAGATE_CXX_STD=ON
    make -j$(nproc)
    make install
    cd /
    rm -rf /tmp/abseil-cpp

    # Update library cache
    ldconfig
"""
before-build = """
    # Ensure protoc is available in PATH
    export PATH="/usr/bin:/usr/local/bin:$PATH"

    cd {project}
    /usr/bin/protoc --cpp_out=src -Isrc src/fixed_dimensional_encoding_config.proto
    cd src && /usr/bin/protoc --python_out=../fixed_dimensional_encoding -I. fixed_dimensional_encoding_config.proto
"""

# macOS-specific configuration
[tool.cibuildwheel.macos]
archs = ["x86_64", "arm64"]
before-all = """
    brew install eigen abseil protobuf
"""
before-build = """
    cd {project}
    # Use full path to protoc to avoid PATH issues
    $(brew --prefix)/bin/protoc --cpp_out=src -Isrc src/fixed_dimensional_encoding_config.proto
    cd src && $(brew --prefix)/bin/protoc --python_out=../fixed_dimensional_encoding -I. fixed_dimensional_encoding_config.proto
"""

[tool.cibuildwheel.macos.environment]
MACOSX_DEPLOYMENT_TARGET = "10.14"

# Windows-specific configuration
[tool.cibuildwheel.windows]
archs = ["AMD64"]
before-all = """
    # Install vcpkg and dependencies
    git clone https://github.com/Microsoft/vcpkg.git C:\\vcpkg
    C:\\vcpkg\\bootstrap-vcpkg.bat
    C:\\vcpkg\\vcpkg.exe install eigen3:x64-windows abseil:x64-windows protobuf:x64-windows
"""
before-build = """
    cd {project}
    # Use full path to protoc from vcpkg
    C:\\vcpkg\\installed\\x64-windows\\tools\\protobuf\\protoc.exe --cpp_out=src -Isrc src/fixed_dimensional_encoding_config.proto
    cd src && C:\\vcpkg\\installed\\x64-windows\\tools\\protobuf\\protoc.exe --python_out=../fixed_dimensional_encoding -I. fixed_dimensional_encoding_config.proto
"""

[tool.cibuildwheel.windows.environment]
CMAKE_TOOLCHAIN_FILE = "C:\\vcpkg\\scripts\\buildsystems\\vcpkg.cmake"

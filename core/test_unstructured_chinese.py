import io
import re
from unstructured.partition.auto import partition

def test_chinese_txt_processing():
    """Test how unstructured processes Chinese text files"""
    
    # 创建一个包含中文的测试文件内容
    chinese_text = """这是一个测试文件。
包含中文内容，用于测试unstructured库的处理能力。

第二段内容：
- 项目1
- 项目2
- 项目3

结尾段落：测试完成。"""
    
    # 模拟当前morphik_parser.py中的处理方式
    print("=== Testing unstructured processing of Chinese TXT file ===")
    
    # 1. 将文本转换为字节（模拟文件上传）
    file_bytes = chinese_text.encode('utf-8')
    print(f"Original text:\n{chinese_text}\n")
    print(f"File bytes length: {len(file_bytes)}")
    
    # 2. 模拟morphik_parser.py中的处理逻辑
    filename = "test_chinese.txt"
    strategy = "fast"
    file_content_type = "text/plain"
    
    print(f"Processing with strategy: {strategy}")
    print(f"Content type: {file_content_type}")
    
    try:
        # 3. 调用unstructured的partition函数（模拟当前代码）
        elements = partition(
            file=io.BytesIO(file_bytes),
            content_type=file_content_type,
            metadata_filename=filename,
            strategy=strategy,
        )
        
        print(f"\nNumber of elements extracted: {len(elements)}")
        
        # 4. 模拟morphik_parser.py中的文本连接方式
        extracted_text = "\n\n".join(str(element) for element in elements if str(element).strip())
        
        # 修复后的文本清理逻辑
        # Remove null bytes which can cause issues
        extracted_text = re.sub(r"[\x00\u0000]", "", extracted_text)
        # Remove other control characters except common whitespace and newlines
        # Preserve Unicode characters including Chinese characters
        extracted_text = re.sub(r"[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]", "", extracted_text)
        
        print(f"\nExtracted text:\n{extracted_text}")
        
        # 5. 比较原始文本和提取的文本
        if chinese_text == extracted_text:
            print("\n✅ SUCCESS: Text extracted correctly!")
        else:
            print("\n❌ ISSUE: Text extraction has problems!")
            print("Differences:")
            print(f"Original length: {len(chinese_text)}")
            print(f"Extracted length: {len(extracted_text)}")
            
        # 6. 显示每个元素的详情
        print("\n=== Element Details ===")
        for i, element in enumerate(elements):
            print(f"Element {i+1}: {type(element).__name__}")
            print(f"Content: {str(element)}")
            print("---")
            
    except Exception as e:
        print(f"❌ ERROR: Failed to process file with unstructured: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chinese_txt_processing()
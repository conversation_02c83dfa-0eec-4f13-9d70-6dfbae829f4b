# Purpose: Production Docker Compose configuration
# This file is for end users running the pre-built Morphik image from ghcr.io
# Downloaded by install_docker.sh script during installation
# Use start-morphik.sh to run this with automatic port detection from morphik.toml
# Note: Always mounts morphik.toml for configuration

services:
  morphik:
    image: ghcr.io/morphik-org/morphik-core:latest
    container_name: morphik-app
    ports:
      - "8000:8000"
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik # Used by internal health checks in the container
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Configuration file (always mounted)
      - ./morphik.toml:/app/morphik.toml:ro
      # Persist storage for uploaded files
      - ./storage:/app/storage
      # Persist logs
      - ./logs:/app/logs
      # Cache for HuggingFace models (e.g., rerankers)
      - huggingface_cache:/root/.cache/huggingface
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - morphik-network
    env_file:
      - .env

  worker:
    image: ghcr.io/morphik-org/morphik-core:latest
    container_name: morphik-worker
    # The worker runs as a background job processor, so no ports are exposed.
    command: arq core.workers.ingestion_worker.WorkerSettings
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Configuration file (always mounted)
      - ./morphik.toml:/app/morphik.toml:ro
      - ./storage:/app/storage
      - ./logs:/app/logs
      - huggingface_cache:/root/.cache/huggingface
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - morphik-network
    env_file:
      - .env

  redis:
    image: redis:7-alpine
    container_name: morphik-redis
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - morphik-network

  postgres:
    # Uses a standard image with the pgvector extension pre-installed
    image: pgvector/pgvector:pg16
    container_name: morphik-postgres
    environment:
      - POSTGRES_USER=morphik
      - POSTGRES_PASSWORD=morphik
      - POSTGRES_DB=morphik
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      # The -d flag is important to specify the database name for the readiness check
      test: ["CMD-SHELL", "pg_isready -U morphik -d morphik"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - morphik-network

  ollama:
    image: ollama/ollama:latest
    container_name: morphik-ollama
    # Enable this profile to run Ollama for local models
    profiles:
      - ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - morphik-network

  ui:
    image: node:22-alpine
    container_name: morphik-ui
    # Enable this profile to run the Web UI
    profiles:
      - ui
    working_dir: /app
    command: sh -c "npm install && npm run build && npm start"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - NEXT_PUBLIC_API_URL=http://10.1.177.121:8000
    ports:
      - "3003:3003"
    volumes:
      - ./ee/ui-component:/app
      - ui_node_modules:/app/node_modules
      - ui_next:/app/.next
    depends_on:
      - morphik
    networks:
      - morphik-network

networks:
  morphik-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  huggingface_cache:
  ui_node_modules:
  ui_next:
